#  make 3-4 tools for trip agent

from langchain.tools import tool
from app.utils.llm import llm
import os
import requests
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.prebuilt import ToolNode
from langgraph.prebuilt import tools_condition
from langchain_core.messages import HumanMessage
from collections import defaultdict
from datetime import datetime
from typing import List, Dict, Any


@tool
def get_weather(city: str, time_interval: str) -> str:
    """
    Get weather forecast for the given city using OpenWeatherMap 5-day/3-hour forecast API.

    Args:
        city (str): The city name.
        time_interval (str): Number of days to fetch data for (max 5).

    Returns:
        str: Daily weather summary for each day.
    """
    try:
        api_key = os.getenv("OPENWEATHERMAP_API_KEY")
        if not api_key:
            return "API key not set for OpenWeatherMap."

        # Cap days at 5 since OWM 5-day forecast only gives 5 days
        days_requested = min(int(time_interval), 5)

        url = f"https://api.openweathermap.org/data/2.5/forecast?q={city}&appid={api_key}&units=metric"
        response = requests.get(url)
        data = response.json()

        if data.get("cod") != "200":
            return f"Error: {data.get('message', 'Could not retrieve weather')}"

        forecast_list = data.get("list", [])
        daily_summary = defaultdict(list)

        # Group data by date
        for entry in forecast_list:
            date_str = entry['dt_txt'].split(' ')[0]
            weather_desc = entry['weather'][0]['description']
            temp = entry['main']['temp']
            humidity = entry['main']['humidity']
            daily_summary[date_str].append((weather_desc, temp, humidity))

        result = f"Weather Forecast for {city}:\n"
        count = 0

        for date, values in sorted(daily_summary.items()):
            if count >= days_requested:
                break
            count += 1
            avg_temp = sum(temp for _, temp, _ in values) / len(values)
            avg_humidity = sum(h for _, _, h in values) / len(values)
            descriptions = [desc for desc, _, _ in values]
            most_common_desc = max(set(descriptions), key=descriptions.count)

            formatted_date = datetime.strptime(date, "%Y-%m-%d").strftime("%d %B %Y")
            result += f"{formatted_date}: {most_common_desc}, Avg Temp: {avg_temp:.1f}°C, Humidity: {avg_humidity:.0f}%\n"

        return result.strip()

    except Exception as e:
        return f"Failed to fetch weather data: {str(e)}"



@tool
def get_top_attractions_tavily(place: str, limit: int = 5) -> List[str]:
    """
    Fetch top attractions/activities for a given place using the Tavily API.

    Args:
        place (str): City or place name.
        limit (int): Max number of attractions to return.

    Returns:
        List[str]: List of attraction names with a short description.
    """
    api_key = os.getenv("TAVILY_API_KEY")
    if not api_key:
        return ["Error: TAVILY_API_KEY not set in environment."]

    # Step 1: (Optional) Geocode the place name, if Tavily requires lat/lon.
    # You can skip this if Tavily supports direct text search.
    geo_resp = requests.get(
        "https://api.tavily.com/v1/geocode",
        headers={"Authorization": f"Bearer {api_key}"},
        params={"q": place}
    ).json()

    if "error" in geo_resp:
        return [f"Geocoding error: {geo_resp['error']}"]
    lat = geo_resp["data"]["lat"]
    lon = geo_resp["data"]["lon"]

    # Step 2: Fetch POIs/Activities from Tavily
    resp = requests.get(
        "https://api.tavily.com/v1/attractions",
        headers={"Authorization": f"Bearer {api_key}"},
        params={
            "latitude": lat,
            "longitude": lon,
            "limit": limit,
            "language": "en"
        }
    )

    if resp.status_code != 200:
        return [f"Tavily API error: {resp.status_code} {resp.text}"]

    items = resp.json().get("data", [])
    if not items:
        return [f"No attractions found for {place}."]

    attractions = []
    for item in items:
        name = item.get("name")
        desc = item.get("short_description") or item.get("description", "")
        desc = (desc[:100] + "...") if len(desc) > 100 else desc
        rating = item.get("rating")
        attractions.append(f"{name} (Rating: {rating}) — {desc}")

    return attractions



@tool
def find_hotels_tavily(
    place: str,
    min_price: float = None,
    max_price: float = None,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """
    Fetch hotels from Tavily for a given place, with optional price filtering.

    Args:
        place (str): City name or place.
        min_price (float, optional): Minimum nightly rate.
        max_price (float, optional): Maximum nightly rate.
        limit (int): Number of hotels to return.

    Returns:
        List[Dict[str, Any]]: Each dict contains name, address, rating, price_per_night.
    """
    api_key = os.getenv("TAVILY_API_KEY")
    if not api_key:
        return [{"error": "TAVILY_API_KEY not set in environment."}]

    # 1. Geocode place to lat/lon
    geo_resp = requests.get(
        "https://api.tavily.com/v1/geocode",
        headers={"Authorization": f"Bearer {api_key}"},
        params={"q": place}
    )
    if geo_resp.status_code != 200:
        return [{"error": f"Geocode error: {geo_resp.status_code}"}]
    geo = geo_resp.json().get("data", {})
    lat, lon = geo.get("lat"), geo.get("lon")
    if lat is None or lon is None:
        return [{"error": f"Could not geocode place: {place}"}]

    # 2. Search hotels
    params: Dict[str, Any] = {
        "latitude": lat,
        "longitude": lon,
        "limit": limit
    }
    if min_price is not None:
        params["min_price"] = min_price
    if max_price is not None:
        params["max_price"] = max_price

    resp = requests.get(
        "https://api.tavily.com/v1/hotels",
        headers={"Authorization": f"Bearer {api_key}"},
        params=params
    )
    if resp.status_code != 200:
        return [{"error": f"Tavily API error: {resp.status_code}"}]

    hotels_data = resp.json().get("data", [])
    if not hotels_data:
        return [{"error": f"No hotels found for {place}."}]

    results: List[Dict[str, Any]] = []
    for h in hotels_data:
        results.append({
            "name": h.get("name"),
            "address": h.get("address", {}).get("full_address"),
            "rating": h.get("rating"),
            "price_per_night": h.get("price", {}).get("currency") + " " + str(h.get("price", {}).get("amount"))
        })

    return results

# -------- Transport Search & Cost Tools --------
@tool
def search_transport_options(
    origin: str,
    destination: str,
    departure_date: str,
    return_date: str,
    num_adults: int) -> List[Dict[str, Any]]:
    """
    Search for available transport options between two locations.

    Args:
        origin (str): The origin location.
        destination (str): The destination location.
        departure_date (str): The departure date in YYYY-MM-DD format.
        return_date (str): The return date in YYYY-MM-DD format.
        num_adults (int): Number of adults.

    Returns:
        List[Dict[str, Any]]: List of available transport options.
    """
    api_key = os.getenv("TAVILY_API_KEY")
    if not api_key:
        return [{"error": "TAVILY_API_KEY not set in environment."}]

    # 1. Geocode origin and destination
    geo_resp = requests.get(
        "https://api.tavily.com/v1/geocode",
        headers={"Authorization": f"Bearer {api_key}"},
        params={"q": origin}
    )
    if geo_resp.status_code != 200:
        return [{"error": f"Geocode error: {geo_resp.status_code}"}]
    geo = geo_resp.json().get("data", {})
    origin_lat, origin_lon = geo.get("lat"), geo.get("lon")
    if origin_lat is None or origin_lon is None:
        return [{"error": f"Could not geocode origin: {origin}"}]

    geo_resp = requests.get(
        "https://api.tavily.com/v1/geocode",
        headers={"Authorization": f"Bearer {api_key}"},
        params={"q": destination}
    )
    if geo_resp.status_code != 200:
        return [{"error": f"Geocode error: {geo_resp.status_code}"}]
    geo = geo_resp.json().get("data", {})
    destination_lat, destination_lon = geo.get("lat"), geo.get("lon")
    if destination_lat is None or destination_lon is None:
        return [{"error": f"Could not geocode destination: {destination}"}]

    # 2. Search transport options
    params: Dict[str, Any] = {
        "origin": origin_lat,
        "destination": destination_lat,
        "departure_date": departure_date,
        "return_date": return_date,
        "num_adults": num_adults,
        "limit": 5
    }

    resp = requests.get(
        "https://api.tavily.com/v1/transport",
        headers={"Authorization": f"Bearer {api_key}"},
        params=params
    )
    if resp.status_code != 200:
        return [{"error": f"Tavily API error: {resp.status_code}"}]

    transport_data = resp.json().get("data", [])
    if not transport_data:
        return [{"error": f"No transport options found for {origin} to {destination}."}]

    results: List[Dict[str, Any]] = []
    for t in transport_data:
        results.append({
            "name": t.get("name"),
            "address": t.get("address", {}).get("full_address"),
            "rating": t.get("rating"),
            "price_per_night": t.get("price", {}).get("currency") + " " + str(t.get("price", {}).get("amount"))
        })

    return results

@tool
def get_transport_cost(
    origin: str,
    destination: str,
    departure_date: str,
    return_date: str,
    num_adults: int) -> float:
    """
    Fetch the cost of transport between two locations.
    returns the cost of transport between two locations.
    """
    api_key = os.getenv("TAVILY_API_KEY")
    if not api_key:
        return {"error": "TAVILY_API_KEY not set in environment."}
    
    # 1. Geocode origin and destination
    geo_resp = requests.get(
        "https://api.tavily.com/v1/geocode",
        headers={"Authorization": f"Bearer {api_key}"},
        params={"q": origin}
    )
    if geo_resp.status_code != 200:
        return {"error": f"Geocode error: {geo_resp.status_code}"}
    geo = geo_resp.json().get("data", {})
    origin_lat, origin_lon = geo.get("lat"), geo.get("lon")
    if origin_lat is None or origin_lon is None:
        return {"error": f"Could not geocode origin: {origin}"}

    geo_resp = requests.get(
        "https://api.tavily.com/v1/geocode",
        headers={"Authorization": f"Bearer {api_key}"},
        params={"q": destination}
    )
    if geo_resp.status_code != 200:
        return {"error": f"Geocode error: {geo_resp.status_code}"}
    geo = geo_resp.json().get("data", {})
    destination_lat, destination_lon = geo.get("lat"), geo.get("lon")
    if destination_lat is None or destination_lon is None:
        return {"error": f"Could not geocode destination: {destination}"}

    # 2. Search transport options
    params: Dict[str, Any] = {
        "origin": origin_lat,
        "destination": destination_lat,
        "departure_date": departure_date,
        "return_date": return_date,
        "num_adults": num_adults,
        "limit": 5
    }

    resp = requests.get(
        "https://api.tavily.com/v1/transport",
        headers={"Authorization": f"Bearer {api_key}"},
        params=params
    )
    if resp.status_code != 200:
        return {"error": f"Tavily API error: {resp.status_code}"}

    transport_data = resp.json().get("data", [])
    if not transport_data:
        return {"error": f"No transport options found for {origin} to {destination}."}

    return transport_data[0].get("price", {}).get("amount")


@tool
def multiply(a: int, b: int) -> int:
    """
    Multiply two integers.

    Args:
        a (int): The first integer.
        b (int): The second integer.

    Returns:
        int: The product of a and b.
    """
    return a * b

@tool
def add(a: int, b: int) -> int:
    """
    Add two integers.

    Args:
        a (int): The first integer.
        b (int): The second integer.

    Returns:
        int: The sum of a and b.
    """
    return a + b

@tool
def divide(a: int, b: int) -> float:
    """
    Divide two integers.

    Args:
        a (int): The numerator.
        b (int): The denominator (must not be 0).

    Returns:
        float: The result of division.
    """
    if b == 0:
        raise ValueError("Denominator cannot be zero.")
    return a / b

@tool
def self_reflection(question: str) -> str:
    """
    Reflect on the question and provide a response.

    Args:
        question (str): The question to reflect on.

    Returns:
        str: A response based on the question.
    """
    return f"I am a helpful travel assistant. I can help you plan a trip from {question}."

@tool
def summary(text: str) -> str:
    """
    Summarize the given text.

    Args:
        text (str): The text to summarize.

    Returns:
        str: A summary of the text in structured JSON format.
    """
    return json.dumps(text)

class Tools:
    def __init__(self):
        self.tools = [multiply, add, divide, get_weather, get_top_attractions_tavily, find_hotels_tavily, search_transport_options , get_transport_cost, self_reflection, summary]
        self.llm_with_tools = llm.bind_tools(self.tools)
        llm_with_tools = self.llm_with_tools

        def router_function(state: MessagesState):
                SYSTEM_PROMPT =      """
                    You are TravelGPT, an expert AI travel agent and budget planner. 
                    Your goal is to craft end‑to‑end trip itineraries with real‑time data and clear cost breakdowns.

                    Responsibilities:
                    1. Clarify the user’s travel parameters (destination, dates, interests, transport mode, budget).
                    2. Use tools to fetch:
                    • Weather forecasts
                    • Top local attractions
                    • Hotel options and nightly rates
                    • Transport availability and fares
                    3. Calculate total and per‑day costs (hotels + transport + estimated activities).
                    4. Build a day‑by‑day schedule, weaving in must‑see sites, optimal travel times, and weather considerations.
                    5. Conclude with a concise summary: dates, itinerary highlights, weather expectations, total budget in local currency, and any tips.

                    Guidelines:
                    - Always rely on tools for factual, up‑to‑date data.
                    - Keep responses clear, structured, and user‑focused.
                    - Ask follow‑up questions if any detail (dates, budget, transport mode) is missing.

                    Let’s plan an unforgettable trip!
                    """


                user_messages = state["messages"]

                # Ensure all messages are properly formatted
                input_messages = [SYSTEM_PROMPT] + user_messages

                # Invoke the LLM with tools
                response = llm_with_tools.invoke(input_messages)

                return {
                    "messages": [response]
                }


        workflow = StateGraph(MessagesState)
        workflow.add_node("llm_decision_step", router_function)
        workflow.add_node("tools", ToolNode(self.tools))

        workflow.add_edge(START, "llm_decision_step")
        workflow.add_conditional_edges("llm_decision_step", tools_condition)
        workflow.add_edge("tools", "llm_decision_step")
        workflow.add_edge("llm_decision_step", END)

        self.app = workflow.compile()

    def get_agent(self):
        return self.app