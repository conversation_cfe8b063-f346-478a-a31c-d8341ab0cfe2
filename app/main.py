from fastapi import <PERSON><PERSON><PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, List, Optional
from app.feature.trip_agent.routes import router as trip_agent_router
import uvicorn

app = FastAPI(
    title="Trip Planner API",
    description="Simple API for trip planning",
    version="0.1.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(trip_agent_router)

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)