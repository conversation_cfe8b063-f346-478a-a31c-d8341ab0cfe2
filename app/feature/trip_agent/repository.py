from app.utils.tools import Tools, summary
from app.utils.llm import llm
from app.feature.trip_agent.schema import ResponseModal, TripAgentRequest
from langchain_core.messages import HumanMessage, AIMessage
import traceback

class TripAgentRepository:
    @staticmethod
    def trip_agent(request: TripAgentRequest):
        try:
            # Initialize the Tools class which sets up the LangGraph workflow
           tools = Tools()
           agent = tools.get_agent()
           user_query = (
    f"Plan a trip from {request.departureLocation} to {request.destinationLocation} "
    f"between {request.startDate} and {request.endDate}, using {request.transportationMode} as the mode of transport. "
    f"The trip should be planned within a budget of {request.budget} {request.currency} for {request.numberOfMembers} members. "
    f"If there are any spelling or grammatical mistakes in the input, correct them intelligently and proceed to plan the trip."
)

           message=[HumanMessage(content=user_query)]
        #    agent.invoke({"messages":message})

           # Important: This returns a generator, you may need to iterate or call `.invoke()` correctly
           result = agent.invoke(
            input={"messages": message},
            config={"recursion_limit": 50}
        )
           # Process the result
           if result and isinstance(result, dict) and "messages" in result:
                # Extract the last AI message from the result
                messages = result["messages"]
                ai_messages = [msg for msg in messages if isinstance(msg, AIMessage)]
                
                if ai_messages:
                    # Get the most recent AI message
                    response_content = ai_messages[-1].content
                else:
                    # Fallback if no AI message is found
                    response_content = "No AI response found in the result."
           else:
                # Fallback for unexpected result format
                response_content = str(result)
                response_content = (response_content)
                
            
           return ResponseModal(message="success", success=True, data=response_content)
            
        except Exception as e:
            # Detailed error logging
            error_msg = f"Error: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            return ResponseModal(message=error_msg, success=False, data=None)