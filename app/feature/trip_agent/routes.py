from fastapi import APIRouter, Request
from app.feature.trip_agent.repository import TripAgentRepository
from app.feature.trip_agent.schema import ResponseModal, TripAgentRequest

router = APIRouter(tags=["user"], prefix="/api/v1/user")


@router.post("/plan-trip", response_model=ResponseModal)
async def trip_agent_check(
    request: TripAgentRequest,
):
    return TripAgentRepository.trip_agent(request)
