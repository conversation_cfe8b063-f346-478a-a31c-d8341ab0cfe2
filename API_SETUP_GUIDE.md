# 🔑 API Setup Guide for Trip Planner

This guide will help you set up all the required API keys for the AI Trip Planner application.

## 📋 Required APIs

### 1. 🤖 OpenAI API (Required)
**Purpose**: Powers the AI agent for trip planning and natural language processing

**How to get**:
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign up or log in to your account
3. Navigate to "API Keys" section
4. Click "Create new secret key"
5. Copy the key (starts with `sk-`)

**Pricing**: Pay-per-use, ~$0.0015 per 1K tokens for GPT-4o-mini
**Free tier**: $5 credit for new accounts

### 2. 🌤️ OpenWeatherMap API (Required)
**Purpose**: Provides weather forecasts for trip destinations

**How to get**:
1. Visit [OpenWeatherMap API](https://openweathermap.org/api)
2. Sign up for a free account
3. Go to "My API Keys" in your account
4. Copy your default API key

**Pricing**: Free tier includes 1,000 calls/day, 60 calls/minute
**Upgrade**: $40/month for 100,000 calls/month

### 3. 🔍 Tavily API (Required)
**Purpose**: Searches for attractions, hotels, and transport options

**How to get**:
1. Visit [Tavily](https://tavily.com/)
2. Sign up for an account
3. Navigate to your dashboard
4. Generate an API key
5. Copy the key (starts with `tvly-`)

**Pricing**: Check Tavily's current pricing plans
**Note**: This API is crucial for finding attractions and accommodations

### 4. 🚀 Groq API (Optional)
**Purpose**: Alternative to OpenAI for faster inference (if you want to switch)

**How to get**:
1. Visit [Groq Console](https://console.groq.com/)
2. Sign up for an account
3. Generate an API key
4. Copy the key (starts with `gsk_`)

**Usage**: Uncomment the Groq configuration in `app/utils/llm.py` to use instead of OpenAI

## 🛠️ Setup Instructions

### Step 1: Copy Environment File
```bash
cp .env.example .env
```

### Step 2: Add Your API Keys
Edit the `.env` file and replace the placeholder values:

```env
# Replace these with your actual API keys
OPENAI_API_KEY=sk-your_actual_openai_key_here
OPENWEATHERMAP_API_KEY=your_actual_weather_key_here
TAVILY_API_KEY=tvly-your_actual_tavily_key_here
```

### Step 3: Verify Setup
Run the application and check the logs for any API key errors.

## 🔒 Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use different keys** for development and production
3. **Rotate keys regularly** for security
4. **Monitor usage** to avoid unexpected charges
5. **Set up billing alerts** on paid services

## 💰 Cost Estimation

For typical usage (10-20 trip plans per day):
- **OpenAI**: ~$5-15/month
- **OpenWeatherMap**: Free (within limits)
- **Tavily**: Varies by plan
- **Total**: ~$10-30/month depending on usage

## 🚨 Troubleshooting

### Common Issues:
1. **"API key not set"** → Check your `.env` file exists and has correct keys
2. **"Invalid API key"** → Verify the key is copied correctly without extra spaces
3. **"Rate limit exceeded"** → You've hit the API limits, wait or upgrade plan
4. **"Network error"** → Check your internet connection and API service status

### Testing API Keys:
```bash
# Test if environment variables are loaded
python -c "import os; print('OpenAI:', os.getenv('OPENAI_API_KEY')[:10] if os.getenv('OPENAI_API_KEY') else 'Not set')"
```

## 📞 Support

If you encounter issues:
1. Check the API provider's documentation
2. Verify your account status and billing
3. Test with a simple API call first
4. Check the application logs for detailed error messages

---

**Note**: Keep your API keys secure and never share them publicly!
