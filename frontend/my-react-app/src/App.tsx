import { useState } from 'react'
import './App.css'
import TripPlannerForm from './components/TripPlannerForm'
import TripPlanResult from './components/TripPlanResult'

function App() {
  const [tripPlanData, setTripPlanData] = useState<string | null>(null)
  const [showResults, setShowResults] = useState(false)

  const handleTripPlanReceived = (data: string) => {
    setTripPlanData(data)
    setShowResults(true)
  }

  const handleBackToForm = () => {
    setShowResults(false)
  }

  return (
    <div className="app-container">
      {!showResults ? (
        <TripPlannerForm onTripPlanReceived={handleTripPlanReceived} />
      ) : (
        <TripPlanResult data={tripPlanData || ''} onBackClick={handleBackToForm} />
      )}
    </div>
  )
}

export default App
