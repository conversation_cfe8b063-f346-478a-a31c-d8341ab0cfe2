import React, { useState, useRef, useEffect } from 'react';
import './TripPlannerForm.css';
import { sampleTripData } from '../test-data';

interface TripFormData {
  departureLocation: string;
  destinationLocation: string;
  startDate: string;
  endDate: string;
  transportationMode: string;
  currency: string;
  budget: string;
  numberOfMembers: string;
}

interface CustomSelectProps {
  options: { value: string; label: string }[];
  value: string;
  onChange: (value: string) => void;
  name: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({ options, value, onChange, name }) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);

  const handleOptionClick = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  const toggleDropdown = () => setIsOpen(!isOpen);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Find the selected option label
  const selectedOption = options.find(option => option.value === value);

  return (
    <div className="custom-select" ref={selectRef}>
      <div className="custom-select-trigger" onClick={toggleDropdown}>
        {selectedOption?.label || 'Select an option'}
        <span className="arrow">{isOpen ? '▲' : '▼'}</span>
      </div>
      {isOpen && (
        <div className="custom-options">
          {options.map((option) => (
            <div
              key={option.value}
              className={`custom-option ${value === option.value ? 'selected' : ''}`}
              onClick={() => handleOptionClick(option.value)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
      <input type="hidden" name={name} value={value} />
    </div>
  );
};

interface TripPlannerFormProps {
  onTripPlanReceived: (data: string) => void;
}

const TripPlannerForm: React.FC<TripPlannerFormProps> = ({ onTripPlanReceived }) => {
  const [formData, setFormData] = useState<TripFormData>({
    departureLocation: '',
    destinationLocation: '',
    startDate: '',
    endDate: '',
    transportationMode: 'airplane',
    currency: 'USD',
    budget: '',
    numberOfMembers: '1'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [dateError, setDateError] = useState<string>('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Clear date error when changing dates
    if (name === 'startDate' || name === 'endDate') {
      setDateError('');
    }
    
    // Special handling for date fields to validate
    if (name === 'endDate' && formData.startDate && value) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(value);
      
      if (endDate < startDate) {
        setDateError('End date cannot be before start date');
      } else {
        setDateError('');
      }
    }
    
    // If changing start date and end date already exists, validate
    if (name === 'startDate' && formData.endDate && value) {
      const startDate = new Date(value);
      const endDate = new Date(formData.endDate);
      
      if (endDate < startDate) {
        setDateError('End date cannot be before start date');
      } else {
        setDateError('');
      }
    }
    
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  const handleCustomSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleTransportationSelect = (mode: string) => {
    setFormData({
      ...formData,
      transportationMode: mode
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate dates before submission
    if (formData.startDate && formData.endDate) {
      const startDate = new Date(formData.startDate);
      const endDate = new Date(formData.endDate);
      
      if (endDate < startDate) {
        setDateError('End date cannot be before start date');
        return;
      }
    }
    
    setIsLoading(true);

    try {
      // Replace with your actual API endpoint
      const response = await fetch('http://localhost:8000/api/v1/user/plan-trip', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setFormSubmitted(true);
          // Pass the trip plan data to the parent component
          onTripPlanReceived(result.data);
          console.log('Trip planning request submitted successfully!');
        } else {
          console.error('Failed to get trip plan data');
        }
      } else {
        console.error('Failed to submit trip planning request');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="trip-planner-container">
      <div className="trip-planner-header">
        <h1>Trip Planner Agent</h1>
        <p>Let us plan your perfect trip with AI assistance</p>
      </div>

      {formSubmitted ? (
        <div className="success-message">
          <h2>Thank you for your submission!</h2>
          <p>Our AI agent is now planning your perfect trip. We'll get back to you shortly with a personalized itinerary.</p>
          <button 
            className="submit-button" 
            onClick={() => setFormSubmitted(false)}
          >
            Plan Another Trip
          </button>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="departureLocation">Departure Location</label>
              <input
                type="text"
                id="departureLocation"
                name="departureLocation"
                placeholder="e.g., New York"
                value={formData.departureLocation}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="destinationLocation">Destination Location</label>
              <input
                type="text"
                id="destinationLocation"
                name="destinationLocation"
                placeholder="e.g., Paris"
                value={formData.destinationLocation}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="startDate">Start Date</label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="endDate">End Date</label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={formData.endDate}
                onChange={handleInputChange}
                min={formData.startDate} // Prevent selecting dates before start date
                required
                className={dateError ? 'input-error' : ''}
              />
              {dateError && <div className="error-message">{dateError}</div>}
            </div>
          </div>
          
          <div className="form-group full-width">
            <label>Transportation Mode</label>
            <div className="transportation-options">
              <div 
                className={`transportation-option ${formData.transportationMode === 'airplane' ? 'selected' : ''}`}
                onClick={() => handleTransportationSelect('airplane')}
              >
                <span role="img" aria-label="airplane">✈️</span>
                <div>Airplane</div>
              </div>
              <div 
                className={`transportation-option ${formData.transportationMode === 'train' ? 'selected' : ''}`}
                onClick={() => handleTransportationSelect('train')}
              >
                <span role="img" aria-label="train">🚆</span>
                <div>Train</div>
              </div>
              <div 
                className={`transportation-option ${formData.transportationMode === 'road' ? 'selected' : ''}`}
                onClick={() => handleTransportationSelect('road')}
              >
                <span role="img" aria-label="car">🚗</span>
                <div>Road</div>
              </div>
            </div>
          </div>

          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="currency">Currency</label>
              <CustomSelect
                name="currency"
                value={formData.currency}
                onChange={(value) => handleCustomSelectChange('currency', value)}
                options={[
                  { value: "USD", label: "USD - US Dollar" },
                  { value: "EUR", label: "EUR - Euro" },
                  { value: "GBP", label: "GBP - British Pound" },
                  { value: "JPY", label: "JPY - Japanese Yen" },
                  { value: "INR", label: "INR - Indian Rupee" },
                  { value: "AUD", label: "AUD - Australian Dollar" },
                  { value: "CAD", label: "CAD - Canadian Dollar" }
                ]}
              />
            </div>

            <div className="form-group">
              <label htmlFor="budget">Budget</label>
              <input
                type="number"
                id="budget"
                name="budget"
                placeholder="e.g., 5000"
                value={formData.budget}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
          
          <div className="form-group">
            <label htmlFor="numberOfMembers">Number of Travelers</label>
            <input
              type="number"
              id="numberOfMembers"
              name="numberOfMembers"
              min="1"
              max="20"
              value={formData.numberOfMembers}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="button-group">
            <button 
              type="submit" 
              className="submit-button"
              disabled={isLoading}
            >
              {isLoading ? 'Planning Your Trip...' : 'Plan My Trip'}
            </button>
            
            {(
              <button 
                type="button" 
                className="test-button"
                onClick={() => onTripPlanReceived(sampleTripData.data)}
              >
                Preview Result
              </button>
            )}
          </div>
        </form>
      )}
    </div>
  );
};

export default TripPlannerForm;
