import React, { useRef } from 'react';
import './TripPlanResult.css';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface TripPlanResultProps {
  data: string;
  onBackClick: () => void;
}

const TripPlanResult: React.FC<TripPlanResultProps> = ({ data, onBackClick }) => {
  const contentRef = useRef<HTMLDivElement>(null);
  
  const handleDownloadPDF = async () => {
    if (!contentRef.current) return;
    
    try {
      // Show loading state
      const downloadButton = document.querySelector('.download-button') as HTMLButtonElement;
      if (downloadButton) {
        downloadButton.textContent = 'Generating PDF...';
        downloadButton.disabled = true;
      }
      
      const content = contentRef.current;
      const canvas = await html2canvas(content, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff'
      });
      
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      // Calculate dimensions
      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      let heightLeft = imgHeight;
      let position = 0;
      const pageHeight = 295; // A4 height in mm
      
      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
      
      // Add additional pages if needed
      while (heightLeft > 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }
      
      // Generate filename with destination and date
      const destination = tripPlan.destination.split(',')[0].trim() || 'Trip';
      const date = new Date().toISOString().split('T')[0];
      const filename = `${destination}-Travel-Plan-${date}.pdf`;
      
      // Save the PDF
      pdf.save(filename);
      
      // Reset button state
      if (downloadButton) {
        downloadButton.textContent = 'Download PDF';
        downloadButton.disabled = false;
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
      
      // Reset button on error
      const downloadButton = document.querySelector('.download-button') as HTMLButtonElement;
      if (downloadButton) {
        downloadButton.textContent = 'Download PDF';
        downloadButton.disabled = false;
      }
    }
  };
  
  interface Section {
    title: string;
    content: string;
  }

  interface TripPlan {
    title: string;
    destination: string;
    dates: string;
    budget: string;
    sections: Section[];
  }

  // Parse and render the trip plan data with better formatting
  const renderTripPlan = (markdownContent: string): TripPlan => {
    // Set default title
    const title = 'Travel Plan';
    
    // Extract key information from the content
    let destination = '';
    let dates = '';
    let budget = '';
    const processedSections: Section[] = [];
    
    // Check if the content has numbered sections (1. **Section Title**:)
    if (markdownContent.match(/\d+\. \*\*.*?\*\*:/)) {
      // This is the numbered list format
      
      // Try to extract destination, dates, and budget from the summary section
      const summaryMatch = markdownContent.match(/\*\*Destination\*\*: ([^\n]+)/i);
      const datesMatch = markdownContent.match(/\*\*Travel Dates\*\*: ([^\n]+)/i);
      const budgetMatch = markdownContent.match(/\*\*Total Budget\*\*: ([^\n]+)/i) || 
                          markdownContent.match(/\*\*Budget\*\*: ([^\n]+)/i);
      
      destination = summaryMatch ? summaryMatch[1].trim() : '';
      dates = datesMatch ? datesMatch[1].trim() : '';
      budget = budgetMatch ? budgetMatch[1].trim() : '';
      
      // Split by numbered sections
      const sectionMatches = markdownContent.match(/\d+\. \*\*.*?\*\*:[\s\S]*?(?=\d+\. \*\*|$)/g) || [];
      
      sectionMatches.forEach(section => {
        // Extract section title
        const titleMatch = section.match(/\d+\. \*\*(.*?)\*\*:/);
        const sectionTitle = titleMatch ? titleMatch[1].trim() : '';
        
        // Extract section content (everything after the title)
        let content = section.replace(/\d+\. \*\*(.*?)\*\*:/, '').trim();
        
        // Process content based on section type
        if (sectionTitle.toLowerCase().includes('weather')) {
          content = content.replace(/\*\*Dates\*\*: (.*?)\./g, '<div class="weather-item"><span class="weather-label">Dates:</span> $1</div>');
          content = content.replace(/\*\*Summary\*\*: (.*?)\./g, '<div class="weather-item"><span class="weather-label">Summary:</span> $1</div>');
        } else if (sectionTitle.toLowerCase().includes('top attractions')) {
          // Format attractions as a list
          const attractions = content.split('\n').filter(line => line.trim() !== '');
          content = '<ul class="attractions-list">';
          attractions.forEach(attraction => {
            content += `<li>${attraction.replace(/^\s*-\s*/, '')}</li>`;
          });
          content += '</ul>';
        } else if (sectionTitle.toLowerCase().includes('hotel')) {
          // Format hotel options
          content = content.replace(/\*\*Hotel \d+\*\*: Name: (.*?), Price: (.*?)\./g, 
            '<div class="hotel-item"><h4>$1</h4><p><strong>Price:</strong> $2</p></div>');
        } else if (sectionTitle.toLowerCase().includes('cost') || sectionTitle.toLowerCase().includes('budget')) {
          // Format budget information
          content = content.replace(/\*\*Total Budget\*\*: (.*?)\./g, 
            '<div class="budget-total"><span>Total Budget:</span> <span>$1</span></div>');
          content = content.replace(/\*\*([^:]+)\*\*: (.*?)\./g, 
            '<div class="budget-item"><span class="budget-category">$1:</span> <span class="budget-value">$2</span></div>');
        } else if (sectionTitle.toLowerCase().includes('itinerary')) {
          // Format itinerary
          const days = content.split('\n').filter(line => line.trim() !== '');
          content = '';
          days.forEach(day => {
            const dayMatch = day.match(/\*\*Day (\d+)\*\*: (.*?)\./);            
            if (dayMatch) {
              content += `<div class="itinerary-day"><span class="day-number">Day ${dayMatch[1]}:</span> ${dayMatch[2]}</div>`;
            } else {
              content += `<div class="itinerary-day">${day}</div>`;
            }
          });
        } else if (sectionTitle.toLowerCase().includes('summary')) {
          // Extract destination, dates, and budget if not already found
          if (!destination) {
            const destMatch = content.match(/\*\*Destination\*\*: ([^\n]+)/i);
            destination = destMatch ? destMatch[1].trim() : '';
          }
          if (!dates) {
            const dateMatch = content.match(/\*\*Travel Dates\*\*: ([^\n]+)/i);
            dates = dateMatch ? dateMatch[1].trim() : '';
          }
          
          // Format summary content
          content = content.replace(/\*\*([^:]+)\*\*: (.*?)\./g, 
            '<div class="summary-item"><span class="summary-label">$1:</span> <span>$2</span></div>');
        } else {
          // Default formatting
          content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        }
        
        processedSections.push({
          title: sectionTitle,
          content: content
        });
      });
    } else {
      // Original format with ### headers
      const destinationMatch = markdownContent.match(/\*\*Destination:\*\* (.*?)\s+/i);
      const datesMatch = markdownContent.match(/\*\*Travel Dates:\*\* (.*?)\s+/i);
      const budgetMatch = markdownContent.match(/\*\*Budget:\*\* (.*?)\s+/i);
      
      destination = destinationMatch ? destinationMatch[1] : '';
      dates = datesMatch ? datesMatch[1] : '';
      budget = budgetMatch ? budgetMatch[1] : '';
      
      // Split the content into main sections using markdown headers
      const mainSections = markdownContent.split(/###\s+/);
      
      mainSections.forEach((section, index) => {
        if (index === 0) return; // Skip the first part (before any headers)
        
        // Extract section title and content
        const lines = section.split('\n');
        const sectionTitle = lines[0].trim();
        const sectionContent = lines.slice(1).join('\n').trim();
        
        // Process content based on section type
        let content = sectionContent;
        
        if (sectionTitle.includes('Weather')) {
          // Format weather information
          content = content.replace(/- \*\*Temperature:\*\* (.*?)\s+/g, '<div class="weather-item"><span class="weather-label">Temperature:</span> $1</div>');
          content = content.replace(/- \*\*Conditions:\*\* (.*?)\s+/g, '<div class="weather-item"><span class="weather-label">Conditions:</span> $1</div>');
        } else if (sectionTitle.includes('Budget Breakdown')) {
          // Format budget information
          content = content.replace(/\d+\. \*\*(.*?):\*\*\s+/g, '<h4>$1:</h4>');
          content = content.replace(/- \*\*(.*?):\*\*\s+(.*?)\s+/g, '<div class="budget-item"><span class="budget-category">$1:</span> <span class="budget-value">$2</span></div>');
          content = content.replace(/\*\*Total Estimated Cost:\*\* (.*?)\s+/g, '<div class="budget-total"><span>Total Estimated Cost:</span> <span>$1</span></div>');
          content = content.replace(/\*\*Remaining Budget:\*\* (.*?)\s+/g, '<div class="budget-remaining"><span>Remaining Budget:</span> <span>$1</span></div>');
        } else if (sectionTitle.includes('Day-wise Itinerary')) {
          // Format itinerary information
          content = content.replace(/####\s+\*\*Day (\d+): (.*?)\*\*/g, '<div class="itinerary-day"><h4>Day $1: $2</h4>');
          content = content.replace(/- \*\*(Morning|Afternoon|Evening):\*\*\s+/g, '<div class="time-slot"><strong>$1:</strong>');
          content = content.replace(/\*\*Hotel Suggestion:\*\*/g, '<strong>Hotel Suggestion:</strong>');
          content = content.replace(/\*\*Name:\*\* (.*?)\s+/g, '<div class="hotel-detail">Name: $1</div>');
          content = content.replace(/\*\*Location:\*\* (.*?)\s+/g, '<div class="hotel-detail">Location: $1</div>');
          content = content.replace(/\*\*Cost:\*\* (.*?)\s+/g, '<div class="hotel-detail">Cost: $1</div></div>');
          
          // Close the time-slot divs
          const timeSlots = content.match(/(<div class="time-slot">.*?)(?=<div class="time-slot">|<\/div>)/gs);
          if (timeSlots) {
            timeSlots.forEach(slot => {
              content = content.replace(slot, slot + '</div>');
            });
          }
          
          // Close the itinerary-day divs
          content = content.replace(/(<div class="itinerary-day">.*?)(?=<div class="itinerary-day">|$)/gs, '$1</div>');
        } else if (sectionTitle.includes('Tips')) {
          // Format tips as a list
          content = content.replace(/- (.*?)\s+/g, '<li>$1</li>');
          content = content.replace(/(<li>.*?<\/li>)/gs, '<ul class="tips-list">$1</ul>');
        } else if (sectionTitle.includes('Final Recommendation')) {
          // Format recommendation
          content = `<div class="final-recommendation">${content}</div>`;
        } else {
          // Default formatting for other sections
          content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
          content = content.replace(/- (.*?)\s+/g, '<li>$1</li>');
          content = content.replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');
        }
        
        processedSections.push({
          title: sectionTitle,
          content: content
        });
      });
    }
    
    return {
      title,
      destination,
      dates,
      budget,
      sections: processedSections
    };
  };
  
  // Parse the markdown content
  const tripPlan = renderTripPlan(data);



  return (
    <div className="trip-result-container">
      <div className="trip-result-header">
        <h1>Your Trip Plan</h1>
        <div className="header-buttons">
          <button className="download-button" onClick={handleDownloadPDF}>
            Download PDF
          </button>
          <button className="back-button" onClick={onBackClick}>
            Plan Another Trip
          </button>
        </div>
      </div>
      
      <div className="trip-result-content" ref={contentRef}>
        <div className="trip-summary">
          <h2>{tripPlan.title}</h2>
          <div className="trip-meta">
            <div className="meta-item">
              <span className="meta-label">Destination:</span>
              <span className="meta-value">{tripPlan.destination}</span>
            </div>
            <div className="meta-item">
              <span className="meta-label">Travel Dates:</span>
              <span className="meta-value">{tripPlan.dates}</span>
            </div>
            <div className="meta-item">
              <span className="meta-label">Budget:</span>
              <span className="meta-value">{tripPlan.budget}</span>
            </div>
          </div>
        </div>
        
        <div className="trip-sections">
          {tripPlan.sections.map((section, index) => (
            <div key={index} className="trip-section">
              <h3 className="section-title">{section.title}</h3>
              <div 
                className="section-content"
                dangerouslySetInnerHTML={{ __html: section.content || '' }} 
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TripPlanResult;
