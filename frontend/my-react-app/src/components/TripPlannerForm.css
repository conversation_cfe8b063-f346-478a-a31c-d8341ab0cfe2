.trip-planner-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  color: white;
  overflow: hidden;
}

.trip-planner-header {
  text-align: center;
  margin-bottom: 2rem;
}

.trip-planner-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.trip-planner-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

/* Style for select dropdown options */
.form-group select option {
  background-color: #6e8efb;
  color: white;
  padding: 10px;
}

/* Custom Select Styling */
.custom-select {
  position: relative;
  width: 100%;
  user-select: none;
}

.custom-select-trigger {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  font-size: 1rem;
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-select-trigger:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.custom-select-trigger .arrow {
  margin-left: 10px;
  transition: transform 0.3s ease;
}

.custom-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #7a8fe8;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  margin-top: 5px;
}

.custom-option {
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.custom-option.selected {
  background-color: rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

/* Dropdown styling for all browsers */
.form-group select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;utf8,<svg fill='white' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
  background-repeat: no-repeat;
  background-position: right 10px center;
  padding-right: 30px;
}

/* For IE */
.form-group select::-ms-expand {
  display: none;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.2);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.date-range-container {
  display: flex;
  gap: 1rem;
}

.date-range-container .date-input {
  flex: 1;
}

.submit-button {
  background: linear-gradient(to right, #5d7ce0, #a777e3);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  flex: 1;
  margin-top: 1rem;
}

.submit-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.submit-button:active {
  transform: translateY(1px);
}

.button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.test-button {
  background: linear-gradient(to right, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  margin-top: 1rem;
}

.test-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.test-button:active {
  transform: translateY(1px);
}

.transportation-options {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.transportation-option {
  flex: 1;
  text-align: center;
  padding: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.transportation-option.selected {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.8);
}

.transportation-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.transportation-option i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.trip-planner-container {
  animation: fadeIn 0.6s ease-out;
}

/* Error styling */
.error-message {
  color: #ffcccc;
  font-size: 0.85rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

.input-error {
  border-color: #ff6b6b !important;
  background-color: rgba(255, 107, 107, 0.1) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-group.full-width {
    grid-column: span 1;
  }
}
