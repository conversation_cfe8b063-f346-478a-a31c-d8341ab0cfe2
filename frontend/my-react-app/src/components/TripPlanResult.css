.trip-result-container {
  max-width: 900px;
  margin: 2rem auto;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  font-family: 'Roboto', sans-serif;
  color: #333;
}

.trip-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.trip-result-header h1 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.8rem;
}

.header-buttons {
  display: flex;
  gap: 1rem;
}

.back-button, .download-button {
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.back-button {
  background-color: #3498db;
  color: white;
}

.back-button:hover {
  background-color: #2980b9;
}

.download-button {
  background-color: #27ae60;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-button:hover {
  background-color: #219653;
}

.download-button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.trip-result-content {
  line-height: 1.6;
}

.trip-summary {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.trip-summary h2 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
}

.trip-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.meta-item {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.meta-label {
  font-weight: 600;
  color: #3498db;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  text-transform: uppercase;
}

.meta-value {
  font-size: 1.1rem;
  color: #2c3e50;
}

.trip-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.trip-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-title {
  color: #3498db;
  font-size: 1.4rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.section-content {
  color: #333;
  line-height: 1.6;
}

/* Specific section styling */
.weather-item {
  margin-bottom: 0.8rem;
  padding: 0.8rem;
  background-color: #e1f5fe;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.weather-label {
  font-weight: 600;
  color: #0288d1;
  margin-right: 1rem;
  min-width: 100px;
}

.attractions-list {
  padding-left: 1.5rem;
}

.attractions-list li {
  margin-bottom: 0.8rem;
  padding: 0.3rem 0;
}

.hotel-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.hotel-item h4 {
  color: #2c3e50;
  margin-bottom: 0.8rem;
  font-size: 1.2rem;
}

.hotel-item p {
  margin: 0.5rem 0;
  color: #555;
}

.itinerary-day {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.itinerary-day h4 {
  margin-top: 0;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.time-slot {
  margin-bottom: 1rem;
  padding-left: 1rem;
  border-left: 3px solid #3498db;
}

.time-slot strong {
  color: #3498db;
  display: block;
  margin-bottom: 0.5rem;
}

.hotel-detail {
  margin-left: 1rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.day-number {
  font-weight: 600;
  color: #0288d1;
  margin-right: 0.5rem;
}

.budget-item, .budget-total, .budget-remaining {
  display: flex;
  justify-content: space-between;
  padding: 0.6rem 0;
  border-bottom: 1px dashed #e0e0e0;
}

.budget-total, .budget-remaining {
  margin-top: 1rem;
  padding-top: 0.8rem;
  border-top: 2px solid #e0e0e0;
  font-weight: 600;
  font-size: 1.1rem;
  color: #2c3e50;
}

.budget-remaining {
  color: #27ae60;
}

.budget-category {
  color: #555;
}

.budget-value {
  font-weight: 500;
}

.recommendations li, .tips-list li {
  margin-bottom: 0.8rem;
  line-height: 1.5;
}

.tips-list {
  background-color: #f8f9fa;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #f39c12;
  margin-left: 0;
  list-style-position: inside;
}

.final-recommendation {
  background-color: #e8f4fd;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #3498db;
  font-style: italic;
  margin-top: 1rem;
  line-height: 1.6;
}

.markdown-content {
  color: #333;
}

.markdown-content h2 {
  color: #2c3e50;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.markdown-content p {
  margin-bottom: 1rem;
}

/* Styling for different sections */
.markdown-content p:has(strong:contains("Destination:")) strong,
.markdown-content p:has(strong:contains("Travel Dates:")) strong,
.markdown-content p:has(strong:contains("Budget:")) strong {
  color: #3498db;
}

/* Responsive design */
@media (max-width: 768px) {
  .trip-result-container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .trip-result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header-buttons {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 0.8rem;
  }
  
  .back-button, .download-button {
    width: 100%;
    justify-content: center;
  }
}
