* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

#root {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.app-container {
  width: 100%;
  max-width: 1200px;
  padding: 2rem 0;
  transition: all 0.3s ease;
}

/* Success message styling */
.success-message {
  text-align: center;
  padding: 2rem;
  animation: fadeIn 0.6s ease-out;
}

.success-message h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.success-message p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #root {
    padding: 1rem;
  }
  
  .app-container {
    padding: 1rem 0;
  }
}
